{"name": "nodejs-be-proxy", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start": "concurrently \"node src/localServer.js\" && yarn sentry:sourcemaps", "dev": "concurrently \"nodemon src/localServer.js\"", "migrate": "babel-node ./node_modules/.bin/sequelize db:migrate", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org thesky9 --project service-nodejs-order ./out && sentry-cli sourcemaps upload --org thesky9 --project service-nodejs-order ./out", "lint": "eslint src", "lint:fix": "eslint src --fix"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-ses": "^3.670.0", "@aws-sdk/credential-provider-env": "^3.667.0", "@sentry/cli": "^2.33.0", "@sentry/node": "^8.22.0", "@sentry/profiling-node": "^8.22.0", "amqplib": "^0.10.4", "axios": "^1.7.7", "basic-auth": "^2.0.1", "cors": "^2.8.5", "csv-parse": "^5.5.6", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "currency": "^4.1.0", "currency.js": "^2.0.4", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "dotenv": "^16.4.5", "express": "^4.19.2", "form-data": "^4.0.0", "fs": "^0.0.1-security", "handlebars": "^4.7.8", "ioredis": "^5.4.1", "lru-cache": "^10.2.2", "mailgun.js": "^10.2.3", "mysql2": "^3.10.2", "nanoid": "^5.0.7", "node-cron": "^3.0.3", "node-telegram-bot-api": "^0.66.0", "nodemailer": "^6.9.15", "p-queue": "^8.0.1", "redis": "^4.6.14", "sequelize": "^6.37.3", "sequelize-cli": "^6.6.2", "tunnel-ssh": "^5.1.2", "util": "^0.12.5", "winston": "^3.13.1", "winston-daily-rotate-file": "^5.0.0", "yarn": "^1.22.22"}, "devDependencies": {"@babel/cli": "^7.24.8", "@babel/core": "^7.26.0", "@babel/eslint-parser": "^7.25.9", "@babel/node": "^7.24.8", "@babel/preset-env": "^7.24.8", "concurrently": "^8.0.1", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "jest": "^29.7.0", "nodemon": "^3.1.4", "prettier": "^3.3.3", "supertest": "^7.0.0"}}