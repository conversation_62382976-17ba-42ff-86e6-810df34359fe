import express from "express";
import dotenv from "dotenv";
import sequelize from "./config/db.js";
import orderRoutes from "./routes/orderRoutes.js";
import routesRemote from "./routes/routesRemote.js";
import { fetchData } from "./utils/httpClient.js";
import { clearCacheByTag, getCacheByTag } from "./config/cache.js";
import logger from "./utils/logger.js"; // Import logger

dotenv.config();

const app = express();
const port = process.env.LOCAL_PORT || 3006;

app.use(express.json());

app.get("/proxy", async (req, res) => {
  const { url } = req.query;
  const domain = req.headers["x-domain"];
  console.log(domain);
  if (!url) {
    return res.status(400).send("url is required");
  }

  try {
    const data = await fetchData(url, { headers: { "x-domain": domain } });
    res.json(data);
  } catch (error) {
    console.log(error);
    logger.error("Error fetching data from proxy", {
      message: error.message,
      stack: error.stack,
    });
    res.status(500).send("Internal Server Error");
  }
});

app.get("/clear-cache", (req, res) => {
  const { tag } = req.query;

  if (!tag) {
    return res.status(400).send("Tag is required");
  }

  try {
    clearCacheByTag(tag);
    res.send(`Cache cleared for tag: ${tag}`);
  } catch (error) {
    logger.error("Error clearing cache", {
      message: error.message,
      stack: error.stack,
    });
    res.status(500).send("Internal Server Error");
  }
});

app.get("/get-cache", async (req, res) => {
  const { tag } = req.query;

  if (!tag) {
    return res.status(400).send("Tag is required");
  }

  try {
    const data = await getCacheByTag(tag);
    res.json(data);
  } catch (error) {
    logger.error("Error getting cache by tag", {
      message: error.message,
      stack: error.stack,
    });
    res.status(500).send("Internal Server Error");
  }
});

app.use("/api", orderRoutes);
app.use("/api", routesRemote);

app.listen(port, async () => {
  try {
    await sequelize.authenticate();
    // await sequelize.sync({ alter: true });
    console.log(
      "Connection to the local database has been established successfully.",
    );
    console.log(`Local server is running on http://localhost:${port}`);
  } catch (error) {
    logger.error("Unable to connect to the local database", {
      message: error.message,
      stack: error.stack,
    });
    console.error("Unable to connect to the local database:", error);
  }
});
