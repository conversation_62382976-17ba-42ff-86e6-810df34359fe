import Redis from "ioredis";
import { LRUCache } from "lru-cache";
import dotenv from "dotenv";
import axios from "axios";

dotenv.config();

const redisUrl = process.env.REDIS_URL;
const enableRedis = process.env.ENABLE_REDIS === "1";
const CACHE_TTL = 3600 * 24 * 90;

let useRedis = false;

const lruCache = new LRUCache({
  max: 100000,
  ttl: CACHE_TTL * 1000,
});

let redisClient;

if (enableRedis) {
  redisClient = new Redis(redisUrl);

  redisClient.on("error", (err) => {
    console.error("Redis error: ", err);
    useRedis = false;
  });

  redisClient.on("connect", () => {
    console.log("Connected to Redis");
    useRedis = true;
  });
} else {
  console.log("Using LRU cache");
}

const getCache = async (key) => {
  const startTime = Date.now();
  try {
    if (useRedis) {
      const data = await redisClient.get(key);
      const duration = Date.now() - startTime;
      if (duration > 100) {
        console.warn(`Slow Redis GET for key ${key}: ${duration}ms`);
      }
      return data ? JSON.parse(data) : null;
    }
    return lruCache.get(key) || null;
  } catch (error) {
    console.error(`Error getting cache for key ${key}:`, error);
    // Fallback to LRU cache if Redis fails
    return lruCache.get(key) || null;
  }
};

const setCache = async (key, value, tags = [], ttl = CACHE_TTL) => {
  const startTime = Date.now();
  try {
    if (useRedis) {
      if (value === null) {
        await redisClient.del(key);
      } else {
        await redisClient.setex(key, ttl, JSON.stringify(value));
        for (const tag of tags) {
          await redisClient.sadd(tag, key);
        }
      }
      const duration = Date.now() - startTime;
      if (duration > 100) {
        console.warn(`Slow Redis SET for key ${key}: ${duration}ms`);
      }
    } else {
      if (value === null) {
        lruCache.delete(key);
      } else {
        lruCache.set(key, value);
        for (const tag of tags) {
          const keys = lruCache.get(tag) || [];
          keys.push(key);
          lruCache.set(tag, keys);
        }
      }
    }
  } catch (error) {
    console.error(`Error setting cache for key ${key}:`, error);
    // Fallback to LRU cache if Redis fails
    if (value === null) {
      lruCache.delete(key);
    } else {
      lruCache.set(key, value);
    }
  }
};

const clearCache = async (key) => {
  if (useRedis) {
    await redisClient.del(key);
  } else {
    lruCache.delete(key);
  }
};

const clearCacheByTag = async (tag) => {
  try {
    await axios.get("https://1siteclone.com/api/revalidate?tag=all");
    console.log("Cache revalidated successfully");
  } catch (error) {
    console.error("Error revalidating cache:", error);
  }

  if (useRedis) {
    const keys = await redisClient.smembers(tag);
    if (keys.length > 0) {
      await redisClient.del(keys);
      await redisClient.del(tag);
    }
  } else {
    const keys = lruCache.get(tag) || [];
    keys.forEach((key) => lruCache.delete(key));
    lruCache.delete(tag);
  }
};

const getCacheByTag = async (tag) => {
  if (useRedis) {
    const keys = await redisClient.smembers(tag);
    if (keys.length === 0) return {};

    const values = await redisClient.mget(keys);
    const result = {};
    keys.forEach((key, index) => {
      try {
        result[key] = JSON.parse(values[index]);
      } catch (parseError) {
        console.error(`Error parsing JSON for key ${key}: `, parseError);
      }
    });
    return result;
  }
  const keys = lruCache.get(tag) || [];
  const result = {};
  keys.forEach((key) => {
    const value = lruCache.get(key);
    if (value !== undefined) {
      result[key] = value;
    }
  });
  return result;
};

const clearCacheByPriority = async (percentage = 20) => {
  if (useRedis) {
    const script = `
            local keys = redis.call('KEYS', '*')
            local totalKeys = #keys
            local keysToDelete = math.floor(totalKeys * tonumber(ARGV[1]) / 100)
            local deleted = 0
            
            table.sort(keys, function(a, b)
                local ttl_a = redis.call('TTL', a)
                local ttl_b = redis.call('TTL', b)
                return ttl_a > ttl_b
            end)
            
            for i = 1, keysToDelete do
                redis.call('DEL', keys[i])
                deleted = deleted + 1
            end
            
            return deleted
        `;
    const result = await redisClient.eval(script, 0, percentage);
    console.log(`Cleared ${result} cache entries (${percentage}% of total)`);
    return result;
  }
  const totalEntries = lruCache.size;
  const entriesToDelete = Math.floor((totalEntries * percentage) / 100);
  let deleted = 0;
  const entries = [...lruCache.entries()].sort((a, b) => a[1].ttl - b[1].ttl);
  for (let i = 0; i < entriesToDelete && i < entries.length; i++) {
    lruCache.delete(entries[i][0]);
    deleted++;
  }
  console.log(`Cleared ${deleted} cache entries from LRU cache`);
  return deleted;
};

const reduceCacheTTL = async (percentage = 90) => {
  if (useRedis) {
    const script = `
            local keys = redis.call('KEYS', '*')
            local modified = 0
            for i, key in ipairs(keys) do
                local ttl = redis.call('TTL', key)
                if ttl > 0 then
                    local newTTL = math.floor(ttl * (100 - tonumber(ARGV[1])) / 100)
                    redis.call('EXPIRE', key, newTTL)
                    modified = modified + 1
                end
            end
            return modified
        `;
    const result = await redisClient.eval(script, 0, percentage);
    console.log(`Reduced TTL for ${result} cache entries by ${percentage}%`);
    return result;
  }
  let modified = 0;
  lruCache.forEach((value, key) => {
    const ttl = lruCache.getTtl(key) - Date.now();
    if (ttl > 0) {
      const newTTL = Math.floor((ttl * (100 - percentage)) / 100);
      lruCache.set(key, value, { ttl: newTTL });
      modified++;
    }
  });
  console.log(
    `Reduced TTL for ${modified} cache entries from LRU cache by ${percentage}%`,
  );
  return modified;
};

const flushAllCache = async () => {
  if (useRedis) {
    await redisClient.flushall();
    console.log("All cache cleared");
  } else {
    lruCache.clear();
    console.log("All cache cleared from LRU cache");
  }
};

// Thêm hàm mới để xóa cache của các version cũ
const clearOldVersionCache = async () => {
  if (useRedis) {
    try {
      const currentVersion = process.env.NEXT_PUBLIC_VERSION;
      const currentPrefix = `next-cache:${currentVersion}`;
      
      // Tìm tất cả các key bắt đầu bằng 'next-cache:'
      const keys = await redisClient.keys('next-cache:*');
      
      // Lọc ra các key không phải version hiện tại
      const oldKeys = keys.filter(key => !key.startsWith(currentPrefix));
      
      if (oldKeys.length > 0) {
        // Xóa tất cả các key cũ
        await redisClient.del(oldKeys);
        console.log(`Cleared ${oldKeys.length} cache entries from old versions`);
        
        // Log các version đã xóa để theo dõi
        const deletedVersions = [...new Set(oldKeys.map(key => {
          const match = key.match(/next-cache:(.*?):/);
          return match ? match[1] : null;
        }).filter(Boolean))];
        
        console.log('Deleted cache for versions:', deletedVersions);
      } else {
        console.log('No old version cache found');
      }
      
      return oldKeys.length;
    } catch (error) {
      console.error('Error clearing old version cache:', error);
      throw error;
    }
  } else {
    console.log('Redis is not enabled');
    return 0;
  }
};

// Thêm hàm mới để xóa tất cả cache của NextJS
const clearAllNextJSCache = async () => {
  if (useRedis) {
    try {
      // Tìm tất cả các key bắt đầu bằng 'next-cache:'
      const keys = await redisClient.keys('next-cache:*');
      
      if (keys.length > 0) {
        // Xóa tất cả các key
        await redisClient.del(keys);
        console.log(`Cleared ${keys.length} NextJS cache entries`);
        
        // Log các version đã xóa để theo dõi
        const deletedVersions = [...new Set(keys.map(key => {
          const match = key.match(/next-cache:(.*?):/);
          return match ? match[1] : null;
        }).filter(Boolean))];
        
        console.log('Deleted cache for versions:', deletedVersions);
      } else {
        console.log('No NextJS cache found');
      }
      
      return keys.length;
    } catch (error) {
      console.error('Error clearing NextJS cache:', error);
      throw error;
    }
  } else {
    console.log('Redis is not enabled');
    return 0;
  }
};

export {
  getCache,
  setCache,
  clearCache,
  clearCacheByTag,
  getCacheByTag,
  CACHE_TTL,
  clearCacheByPriority,
  reduceCacheTTL,
  flushAllCache,
  redisClient,
  clearOldVersionCache,
  clearAllNextJSCache,
};
