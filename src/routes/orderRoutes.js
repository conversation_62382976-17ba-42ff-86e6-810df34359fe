import express from "express";
import basicAuth from "basic-auth";
import { format, subDays } from "date-fns";
import {
  generateProductReport,
  generateReport,
} from "../controllers/report.js";
import {
  createOrder,
  getOrderSum,
  analyzeOrders,
  getOrders,
  getImportedOrders,
  getOrderById,
  updateOrderTracking,
  addNoteToOrder,
  getNotesForOrder,
  updateOrder,
  updateOrderTrackingWithoutEmail,
  createDraftOrderForPayPal,
  handleCapturePayPal,
} from "../controllers/orderController.js";
import { getSaleReport } from "../controllers/saleReport.js";
import { getDomainReport } from "../controllers/domainReport.js";
import { getDomainConfigs } from "../controllers/domainConfigController.js";
import { listTransactions } from "../controllers/transactionController.js";

const router = express.Router();

const auth = (req, res, next) => {
  const user = basicAuth(req);

  if (!user || !user.name || !user.pass) {
    res.set("WWW-Authenticate", 'Basic realm="example"');
    return res.status(401).send("Authentication required.");
  }

  const username = "devtruestore";
  const password = "pas72ns2ws5ord";

  if (user.name === username && user.pass === password) {
    return next();
  }
  res.set("WWW-Authenticate", 'Basic realm="example"');
  return res.status(401).send("Invalid credentials.");
};

router.post("/orders", auth, createOrder);

router.get("/orders/sum", auth, getOrderSum);
router.get("/orders/:orderId", auth, getOrderById);
router.post("/orders/:orderId", auth, updateOrder);
router.post("/orders/:orderId/notes", auth, addNoteToOrder);
router.get("/orders/:orderId/notes", auth, getNotesForOrder);

router.get("/orders/analyzer", auth, analyzeOrders);

router.get("/orders", auth, getOrders);

// router.get("/orders-import", auth, getImportedOrders);

router.get("/v1-domain-report", auth, generateReport);
router.get("/v1-domain-product-report", auth, generateProductReport);
router.get("/sale-report", auth, getSaleReport);
router.get("/domain-report", auth, getDomainReport);
router.get("/domain-configs", auth, getDomainConfigs);

// Add new public route for getting order details
router.get("/public/v3/orders/:orderId", getOrderById);

router.post("/update-tracking", updateOrderTracking);
router.post("/update-tracking-without-email", updateOrderTrackingWithoutEmail);

router.post("/payments/paypal/create", createDraftOrderForPayPal);
router.post("/payments/paypal/capture/:orderId", handleCapturePayPal);

router.get("/transactions", auth, listTransactions);

router.get("/", auth, (req, res) => {
  const yesterday = subDays(new Date(), 1);
  const formattedDate = format(yesterday, "dd-MM-yyyy");

  const apiInfo = [
    {
      method: "POST",
      endpoint: `/api/orders?startdate=${formattedDate}&enddate=${formattedDate}`,
      description: "Create a new order",
      requiredParams: ["domain", "transaction_id", "orderData"],
    },
    {
      method: "GET",
      endpoint: `/api/orders/sum?domain=example.com&startdate=${formattedDate}&enddate=${formattedDate}`,
      description: "Get the sum of orders",
      requiredParams: ["domain", "startdate", "enddate"],
    },
    {
      method: "GET",
      endpoint: `/api/orders/analyzer?startdate=${formattedDate}&enddate=${formattedDate}`,
      description: "Analyze orders within a date range",
      requiredParams: ["startdate", "enddate"],
    },
    {
      method: "GET",
      endpoint: `/api/orders?startdate=${formattedDate}&enddate=${formattedDate}`,
      description: "Get orders within a date range with pagination",
      requiredParams: ["startdate", "enddate"],
      optionalParams: ["domain", "perPage", "page"],
    },
    {
      method: "GET",
      endpoint: `/api/sale-report?date=${formattedDate}`,
      description: "Generate sale report",
      optionalParams: [],
    },
    {
      method: "GET",
      endpoint: `/api/domain-report?date=${formattedDate}`,
      description: "Generate domain product report",
      optionalParams: [],
    },
  ];

  res.json(apiInfo);
});

export default router;
