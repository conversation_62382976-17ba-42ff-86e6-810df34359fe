import express from "express";
import dotenv from "dotenv";
import cors from "cors"; // Import cors
import { fetchData } from "./utils/httpClient.js";
import {
  clearCacheByTag,
  getCacheByTag,
  reduceCacheTTL,
  clearCacheByPriority,
  redisClient,
  flushAllCache,
  clearOldVersionCache,
  clearAllNextJSCache // Thêm import
} from "./config/cache.js";
import logger from "./utils/logger.js";

dotenv.config();

const app = express();
const port = process.env.LOCAL_PORT || 3006;

app.use(cors()); // Use cors middleware to allow all origins
app.use(express.json());

app.get("/proxy", async (req, res) => {
  const { url } = req.query;
  const domain = req.headers["x-domain"];
  if (!url) {
    return res.status(400).send("url is required");
  }

  try {
    const options = {};
    if (domain) {
      options.headers = { "x-domain": domain };
    }
    const data = await fetchData(url, options);
    res.json(data);
  } catch (error) {
    console.log(error);
    logger.error("Error fetching data from proxy", {
      message: error.message,
      stack: error.stack,
    });
    res.status(500).send("Internal Server Error");
  }
});

app.get("/clear-cache", (req, res) => {
  const { tag } = req.query;

  if (!tag) {
    return res.status(400).send("Tag is required");
  }

  try {
    clearCacheByTag(tag);
    res.send(`Cache cleared for tag: ${tag}`);
  } catch (error) {
    logger.error("Error clearing cache", {
      message: error.message,
      stack: error.stack,
    });
    res.status(500).send("Internal Server Error");
  }
});

app.get("/get-cache", async (req, res) => {
  const { tag } = req.query;

  if (!tag) {
    return res.status(400).send("Tag is required");
  }

  try {
    const data = await getCacheByTag(tag);
    res.json(data);
  } catch (error) {
    logger.error("Error getting cache by tag", {
      message: error.message,
      stack: error.stack,
    });
    res.status(500).send("Internal Server Error");
  }
});

app.get("/reduce-cache-ttl", async (req, res) => {
  try {
    const percentage = parseInt(req.query.percentage) || 90;
    const modifiedCount = await reduceCacheTTL(percentage);
    res.json({ message: `Reduced TTL for ${modifiedCount} cache entries by ${percentage}%` });
  } catch (error) {
    logger.error('Error reducing cache TTL', { message: error.message, stack: error.stack });
    res.status(500).json({ error: "Internal Server Error" });
  }
});

app.get("/clear-cache-priority", async (req, res) => {
  try {
    const percentage = parseInt(req.query.percentage) || 20;
    const deletedCount = await clearCacheByPriority(percentage);
    res.json({ message: `Cleared ${deletedCount} cache entries` });
  } catch (error) {
    logger.error('Error clearing cache by priority', { message: error.message, stack: error.stack });
    res.status(500).json({ error: "Internal Server Error" });
  }
});

app.get("/flush-all-cache", async (req, res) => {
  try {
    await flushAllCache();
    res.json({ message: "All cache has been flushed" });
  } catch (error) {
    logger.error('Error flushing all cache', { message: error.message, stack: error.stack });
    res.status(500).json({ error: "Internal Server Error" });
  }
});

app.get("/clear-old-version-cache", async (req, res) => {
  try {
    const deletedCount = await clearOldVersionCache();
    
    if (deletedCount > 0) {
      res.json({ 
        message: `Cleared ${deletedCount} cache entries from old versions`,
        current_version: process.env.NEXT_PUBLIC_VERSION
      });
    } else {
      res.json({ 
        message: "No old version cache found",
        current_version: process.env.NEXT_PUBLIC_VERSION
      });
    }
  } catch (error) {
    logger.error('Error clearing old version cache', { 
      message: error.message, 
      stack: error.stack,
      current_version: process.env.NEXT_PUBLIC_VERSION
    });
    res.status(500).json({ error: "Internal Server Error" });
  }
});

app.get("/cache-stats", async (req, res) => {
  try {
    // If redisClient is not exported directly, you might need to create helper functions in cache.js
    // and import them here instead of using redisClient directly

    // 1. Thông tin về bộ nhớ Redis đang sử dụng
    const memoryInfo = await redisClient.info("memory");

    // 2. Số lượng keys trong Redis
    const dbSize = await redisClient.dbsize();

    // 3. Lấy mẫu các keys và kích thước của chúng
    const sampleSize = 1000; // Số lượng keys mẫu
    const keys = await redisClient.keys('*');
    const sampleKeys = keys.slice(0, sampleSize);
    
    let totalSize = 0;
    const keySizes = await Promise.all(sampleKeys.map(async (key) => {
      const size = await redisClient.memory('usage', key);
      totalSize += size;
      return { key, size };
    }));

    const averageSize = totalSize / sampleKeys.length;
    const estimatedTotalSize = averageSize * keys.length / (1024 * 1024); // Convert to MB

    // 4. Phân tích các loại dữ liệu
    const typeCount = {};
    await Promise.all(sampleKeys.map(async (key) => {
      const type = await redisClient.type(key);
      typeCount[type] = (typeCount[type] || 0) + 1;
    }));

    // Thêm phân tích version
    const versionCount = {};
    await Promise.all(sampleKeys.map(async (key) => {
      const match = key.match(/next-cache:(.*?):/);
      const version = match ? match[1] : 'unknown';
      versionCount[version] = (versionCount[version] || 0) + 1;
    }));

    res.json({
      memoryInfo,
      dbSize,
      sampleSize,
      averageKeySize: averageSize,
      estimatedTotalSize: `${estimatedTotalSize.toFixed(2)} MB`,
      typeDistribution: typeCount,
      versionDistribution: versionCount, // Thêm thông tin về phân bố version
      currentVersion: process.env.NEXT_PUBLIC_VERSION,
      sampleKeySizes: keySizes
    });
  } catch (error) {
    console.error("Error fetching cache stats:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

// Thêm route mới
app.get("/clear-nextjs-cache", async (req, res) => {
  try {
    const deletedCount = await clearAllNextJSCache();
    
    if (deletedCount > 0) {
      res.json({ 
        message: `Cleared ${deletedCount} NextJS cache entries`,
        current_version: process.env.NEXT_PUBLIC_VERSION
      });
    } else {
      res.json({ 
        message: "No NextJS cache found",
        current_version: process.env.NEXT_PUBLIC_VERSION
      });
    }
  } catch (error) {
    logger.error('Error clearing NextJS cache', { 
      message: error.message, 
      stack: error.stack,
      current_version: process.env.NEXT_PUBLIC_VERSION
    });
    res.status(500).json({ error: "Internal Server Error" });
  }
});

app.listen(port, async () => {
  try {
    console.log(
      "Connection to the local database has been established successfully."
    );
    console.log(`Local server is running on http://localhost:${port}`);
  } catch (error) {
    logger.error("Unable to connect to the local database", {
      message: error.message,
      stack: error.stack,
    });
    console.error("Unable to connect to the local database:", error);
  }
});
