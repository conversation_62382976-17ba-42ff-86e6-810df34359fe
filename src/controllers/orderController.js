import { Op, Sequelize } from "sequelize";
import currency from "currency.js";
import {
  updateTracking,
  createNewOrder,
  createNewOrderFromPayPal,
  updateOrderWithPayPalId,
  updateOrderAfterPayPalCapture,
} from "../services/orderService.js";
import { sendToQueue } from "../config/rabbitmq.js";
import logger from "../utils/logger.js";
import {
  formatStats,
  convertToClientTimezone,
  calculateOrderTotals,
  generateOrderKey,
} from "../utils/utils.js";

import Order from "../models/order.js";
import OrderSync from "../models/orderSync.js";
import OrderNote from "../models/orderNote.js";
import {
  createPayPalOrder,
  capturePayPalOrder,
  checkPaymentStatus
} from "../services/payPalService.js";
import config from "../config/config.js";
import TransactionLogger from "../services/transactionLogger.js";

const updateOrderTracking = async (req, res) => {
  try {
    const { transaction_id, tracking_number } = req.body;

    if (!transaction_id || !tracking_number) {
      return res.status(400).json({
        error: "Missing transaction_id or tracking_number",
      });
    }

    const result = await updateTracking(transaction_id, tracking_number);

    // Return response immediately
    res.status(201).json({
      message: "Successfully updated tracking",
      transaction_id,
      port: result.successfulPort,
      domain: result.domain,
    });

    // Non-blocking email operation - execute after response is sent
    setImmediate(async () => {
      // Prepare and send email
      const emailData = {
        id: result.order.id,
        order_key: result.order.order_key,
        domain: result.order.domain,
        transaction_id: result.order.transaction_id,
        orderData: result.order.orderData,
        status: result.order.status,
        tracking_id: result.order.tracking_id,
        createdAt: result.order.createdAt,
        updatedAt: result.order.updatedAt,
      };

      try {
        await sendToQueue("EMAIL", "shipping_confirmation", emailData);
        logger.info("Shipping confirmation email queued", {
          orderId: result.order.id,
        });
      } catch (emailError) {
        logger.error("Failed to queue shipping confirmation email", {
          error: emailError.message,
          stack: emailError.stack,
          orderId: result.order.id,
        });
      }
    });

  } catch (error) {
    logger.error("Error in updateOrderTracking", {
      error: error.message,
      stack: error.stack,
      transaction_id: req.body.transaction_id,
      tracking_number: req.body.tracking_number,
    });

    if (error.response && error.response.status) {
      return res.status(error.response.status).json({
        error: error.response.data || "Error processing request",
      });
    }

    return res.status(500).json({
      error: error.message || "Internal Server Error",
    });
  }
};

const updateOrderTrackingWithoutEmail = async (req, res) => {
  try {
    const { transaction_id, tracking_number } = req.body;

    if (!transaction_id || !tracking_number) {
      return res.status(400).json({
        error: "Missing transaction_id or tracking_number",
      });
    }

    const result = await updateTracking(transaction_id, tracking_number);
    console.log(
      "updateOrderTrackingWithoutEmail",
      transaction_id,
      tracking_number
    );
    res.status(201).json({
      message: "Successfully updated tracking without sending email",
      transaction_id,
      port: result.successfulPort,
      domain: result.domain,
    });
  } catch (error) {
    logger.error("Error in updateOrderTrackingWithoutEmail", {
      error: error.message,
      stack: error.stack,
      transaction_id: req.body.transaction_id,
      tracking_number: req.body.tracking_number,
    });

    if (error.response && error.response.status) {
      return res.status(error.response.status).json({
        error: error.response.data || "Error processing request",
      });
    }

    return res.status(500).json({
      error: error.message || "Internal Server Error",
    });
  }
};

const createOrder = async (req, res) => {
  try {
    if (!req.body.domain || !req.body.transaction_id || !req.body.orderData) {
      return res.status(400).json({
        error: "domain, transaction_id, and orderData are required",
      });
    }

    const result = await createNewOrder(req.body);
    const formattedStats = formatStats(result.stats);
    result.order.stats = formattedStats;

    // Immediately return response to client without waiting for email/TrueStore
    res.status(201).json(result.order);

    // Non-blocking async operations - don't await, just fire and forget
    setImmediate(async () => {
      if (!config.disableEmailSending) {
        try {
          await sendToQueue("EMAIL", "order_confirmation", {
            id: result.order.id,
            order_key: result.order.order_key,
            domain: result.order.domain,
            transaction_id: result.order.transaction_id,
            orderData: result.orderData,
            status: result.order.status,
            createdAt: result.order.createdAt,
            updatedAt: result.order.updatedAt,
          });
          logger.info("Order confirmation email queued", {
            orderId: result.order.id,
          });
        } catch (emailError) {
          logger.error("Failed to queue order confirmation email", {
            error: emailError.message,
            stack: emailError.stack,
            orderId: result.order.id,
          });
        }
      }

      if (!config.disableTrueStoreSync) {
        try {
          await sendToQueue("TRUESTORE", "new_order", {
            order: result.order,
            stats: formattedStats
          });
          logger.info("Order queued for TrueStore", {
            orderId: result.order.id,
            orderKey: result.order.order_key,
            domain: result.order.domain,
          });
        } catch (trueStoreError) {
          logger.error("Error queueing order for TrueStore", {
            orderId: result.order.id,
            orderKey: result.order.order_key,
            domain: result.order.domain,
            error: trueStoreError.message,
            stack: trueStoreError.stack,
          });
        }
      }
    });

  } catch (error) {
    logger.error("Error in createOrder", {
      error: error.message,
      stack: error.stack,
      body: req.body,
    });

    return res.status(500).json({
      error: error.message || "Internal Server Error",
    });
  }
};

const getOrderSum = async (req, res) => {
  const { domain, startdate, enddate } = req.query;

  if (!domain || !startdate || !enddate) {
    return res.status(400).send("domain, startdate and enddate are required");
  }

  try {
    const { startDate, endDate } = convertToClientTimezone(startdate, enddate);

    const count = await Order.count({
      where: {
        domain,
        createdAt: {
          [Op.between]: [startDate, endDate],
        },
      },
    });

    res.json(count);
  } catch (error) {
    logger.error("Error getting order sum", {
      message: error.message,
      stack: error.stack,
      sql: error.sql,
      sqlState: error.sqlState,
      errno: error.errno,
    });
    res.status(500).send("Internal Server Error");
  }
};

const analyzeOrders = async (req, res) => {
  const { startdate, enddate } = req.query;

  if (!startdate || !enddate) {
    return res.status(400).send("startdate and enddate are required");
  }

  try {
    const { startDate, endDate } = convertToClientTimezone(startdate, enddate);

    const orders = await Order.findAll({
      attributes: [
        [Sequelize.fn("DATE", Sequelize.col("createdAt")), "date"],
        [Sequelize.fn("COUNT", Sequelize.col("id")), "total_orders"],
      ],
      where: {
        createdAt: {
          [Op.between]: [startDate, endDate],
        },
      },
      group: ["date"],
      order: [[Sequelize.col("date"), "ASC"]],
    });

    res.json(orders);
  } catch (error) {
    logger.error("Error analyzing orders", {
      message: error.message,
      stack: error.stack,
      sql: error.sql,
      sqlState: error.sqlState,
      errno: error.errno,
    });
    res.status(500).send("Internal Server Error");
  }
};

const getOrders = async (req, res) => {
  const {
    startdate,
    enddate,
    domain,
    status,
    paypal_client_id,
    perPage = 10,
    page = 1,
    orderBy = "createdAt",
    sort = "desc",
  } = req.query;

  if (!startdate || !enddate) {
    return res.status(400).send("startdate and enddate are required");
  }

  const perPageInt = Math.min(Math.max(parseInt(perPage, 10), 1), 100);
  const pageInt = Math.max(parseInt(page, 10), 1);

  try {
    const { startDate, endDate } = convertToClientTimezone(startdate, enddate);

    const whereClause = {
      createdAt: {
        [Op.between]: [startDate, endDate],
      },
    };

    if (domain) {
      whereClause.domain = domain;
    }

    if (status) {
      const statusArray = status.split(",").map((s) => s.trim());
      if (statusArray.length > 0) {
        whereClause.status = {
          [Op.in]: statusArray,
        };
      }
    }

    if (paypal_client_id) {
      whereClause.paypal_client_id = paypal_client_id;
    }

    const orders = await Order.findAndCountAll({
      where: whereClause,
      limit: perPageInt,
      offset: (pageInt - 1) * perPageInt,
      order: [[orderBy, sort]],
    });

    res.json({
      totalItem: orders.count,
      totalPage: Math.ceil(orders.count / perPageInt),
      perPage: perPageInt,
      page: pageInt,
      data: orders.rows,
    });
  } catch (error) {
    logger.error("Error getting orders", {
      message: error.message,
      stack: error.stack,
      sql: error.sql,
      sqlState: error.sqlState,
      errno: error.errno,
    });
    res.status(500).send("Internal Server Error");
  }
};

const getImportedOrders = async (req, res) => {
  const { domain, perPage = 10, page = 1 } = req.query;

  const perPageInt = Math.min(Math.max(parseInt(perPage, 10), 1), 100);
  const pageInt = Math.max(parseInt(page, 10), 1);

  try {
    const whereClause = {};

    if (domain) {
      whereClause.domain = domain;
    }

    const orders = await OrderSync.findAndCountAll({
      where: whereClause,
      limit: perPageInt,
      offset: (pageInt - 1) * perPageInt,
      order: [["createdAt", "DESC"]],
      include: [
        {
          model: Order,
          as: "order",
        },
      ],
    });

    res.json({
      totalItem: orders.count,
      totalPage: Math.ceil(orders.count / perPageInt),
      perPage: perPageInt,
      page: pageInt,
      data: orders.rows,
    });
  } catch (error) {
    logger.error("Error getting imported orders", {
      message: error.message,
      stack: error.stack,
      sql: error.sql,
      sqlState: error.sqlState,
      errno: error.errno,
    });
    res.status(500).send("Internal Server Error");
  }
};

const getOrderById = async (req, res) => {
  let orderId;

  try {
    orderId = req.params.orderId;
    if (!orderId) {
      return res.status(400).send("Order id is required");
    }

    let whereCondition;
    if (isNaN(orderId)) {
      whereCondition = { transaction_id: orderId };
    } else {
      whereCondition = { id: parseInt(orderId, 10) };
    }

    const order = await Order.findOne({
      where: whereCondition,
    });

    if (!order) {
      return res.status(404).send("Order not found");
    }
    const formattedResponse = {
      id: order.id,
      order_key: order.order_key,
      domain: order.domain,
      transaction_id: order.transaction_id,
      paypal_client_id: order.paypal_client_id,
      status: order.status,
      tracking_id: order.tracking_id,
      port: order.port,
      paypal_order_id: order.paypal_order_id,
      paypal_checked_at: order.paypal_checked_at,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      orderData: order.orderData
    };

    res.json(formattedResponse);
  } catch (error) {
    logger.error("Error getting order", {
      message: error.message,
      stack: error.stack,
      orderId,
    });
    res.status(500).send("Internal Server Error");
  }
};

const addNoteToOrder = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { note } = req.body;

    if (!note) {
      return res.status(400).send("Note is required");
    }

    const order = await Order.findByPk(orderId);
    if (!order) {
      return res.status(404).send("Order not found");
    }

    const newNote = await OrderNote.create({
      orderId,
      note,
      createdBy: req.user ? req.user.username : "system", // Assuming you have user information in the request
    });

    res.status(201).json(newNote);
  } catch (error) {
    logger.error("Error adding note to order", {
      message: error.message,
      stack: error.stack,
    });
    res.status(500).send("Internal Server Error");
  }
};

const getNotesForOrder = async (req, res) => {
  try {
    const { orderId } = req.params;

    const notes = await OrderNote.findAll({
      where: { orderId },
      order: [["createdAt", "DESC"]],
    });

    res.json(notes);
  } catch (error) {
    logger.error("Error getting notes for order", {
      message: error.message,
      stack: error.stack,
    });
    res.status(500).send("Internal Server Error");
  }
};

const updateOrder = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { status, billing, shipping, transaction_id, set_paid, meta_data } =
      req.body;

    const order = await Order.findByPk(orderId);
    if (!order) {
      return res.status(404).send("Order not found");
    }

    if (status) {
      order.status = status;
    }

    if (set_paid !== undefined) {
      order.isPaid = set_paid;
    }

    // Update orderData
    const updatedOrderData = { ...order.orderData };
    if (billing) updatedOrderData.billing = billing;
    if (shipping) updatedOrderData.shipping = shipping;
    if (transaction_id) updatedOrderData.transaction_id = transaction_id;
    if (meta_data) {
      updatedOrderData.meta_data = updatedOrderData.meta_data || [];
      updatedOrderData.meta_data = [
        ...updatedOrderData.meta_data,
        ...meta_data,
      ];
    }

    order.orderData = updatedOrderData;

    await order.save();

    res.json(order);
  } catch (error) {
    console.log(error);
    logger.error("Error updating order", {
      message: error.message,
      stack: error.stack,
    });
    res.status(500).send("Internal Server Error");
  }
};

const createDraftOrderForPayPal = async (req, res) => {
  console.time("createDraftOrderForPayPal - Total");
  try {
    // Validation & Setup section
    let validationResult;
    try {
      const {
        domain,
        paypal_client_id,
        payment_method,
        shipping_lines,
        meta_data,
        set_paid,
        billing,
        shipping,
        payment_method_title,
        line_items,
        free_shipping,
        discount,
        tip,
        email,
        currency = "USD" // Default to USD if not provided for backward compatibility
      } = req.body;

      // Log request data for debugging
      logger.info(`[${domain}] Creating PayPal draft order`, {
        domain,
        paypal_client_id,
        currency,
        total: req.body.total,
        requestBody: req.body
      });

      if (!domain || !paypal_client_id || !line_items) {
        console.timeEnd("createDraftOrderForPayPal - Total");
        return res.status(400).json({
          error: "Missing required fields",
        });
      }

      // Validate currency code (optional)
      const validCurrencies = ["USD", "EUR", "GBP", "AUD", "CAD"]; // Add more as needed
      if (!validCurrencies.includes(currency)) {
        logger.warn("Invalid currency code, using USD", {
          providedCurrency: currency,
          defaultingTo: "USD"
        });
      }

      // Extract invoice_id and funding_source from meta_data
      const invoice_id = meta_data.find(
        (item) => item.key === "invoice_id"
      )?.value;
      const funding_source = meta_data.find(
        (item) => item.key === "funding_source"
      )?.value;

      if (!invoice_id || !funding_source) {
        console.timeEnd("createDraftOrderForPayPal - Total");
        return res.status(400).json({
          error: "Missing invoice_id or funding_source in meta_data",
        });
      }

      validationResult = {
        domain, paypal_client_id, payment_method, shipping_lines, meta_data,
        set_paid, billing, shipping, payment_method_title, line_items,
        free_shipping, discount, tip, email, currency, invoice_id, funding_source
      };
    } catch (validationError) {
      logger.error(`[${validationResult?.domain || 'unknown'}] Error in validation step:`, validationError);
      throw validationError;
    }

    // Calculate Totals section
    let calculationResult;
    try {
      const {
        total,
        subTotal: sub_total,
        shippingTotal: shipping_total,
        discountTotal: discount_total,
        tipTotal: tip_total
      } = calculateOrderTotals(
        validationResult.line_items,
        validationResult.shipping_lines,
        validationResult.free_shipping,
        validationResult.discount,
        validationResult.tip
      );

      const order_key = generateOrderKey();

      calculationResult = {
        total, sub_total, shipping_total, discount_total, tip_total, order_key
      };
    } catch (calculationError) {
      logger.error(`[${validationResult.domain}] Error in calculation step:`, calculationError);
      throw calculationError;
    }

    // Create Draft Order DB section
    console.time(`[${validationResult.domain}] createDraftOrderForPayPal - Create Draft Order DB`);
    let draftOrder;
    try {
      draftOrder = await createNewOrderFromPayPal({
        status: "draft",
        domain: validationResult.domain,
        paypal_client_id: validationResult.paypal_client_id,
        order_key: calculationResult.order_key,
        orderData: {
          payment_method: validationResult.payment_method,
          shipping_lines: validationResult.shipping_lines,
          meta_data: validationResult.meta_data,
          set_paid: validationResult.set_paid,
          billing: validationResult.billing,
          shipping: validationResult.shipping,
          payment_method_title: validationResult.payment_method_title,
          total: calculationResult.total,
          shipping_total: calculationResult.shipping_total,
          sub_total: calculationResult.sub_total,
          discount_total: calculationResult.discount_total,
          tip_total: calculationResult.tip_total,
          line_items: validationResult.line_items,
          funding_source: validationResult.funding_source,
          invoice_id: validationResult.invoice_id,
          free_shipping: validationResult.free_shipping,
          email: validationResult.email,
          currency: validationResult.currency
        },
      });
    } catch (dbError) {
      console.timeEnd(`[${validationResult.domain}] createDraftOrderForPayPal - Create Draft Order DB`);
      logger.error(`[${validationResult.domain}] Error in database creation step:`, dbError);
      throw dbError;
    }
    console.timeEnd(`[${validationResult.domain}] createDraftOrderForPayPal - Create Draft Order DB`);

    // Create PayPal Order section
    console.time(`[${validationResult.domain}] createDraftOrderForPayPal - Create PayPal Order`);
    let paypalOrder;
    try {
      paypalOrder = await createPayPalOrder(validationResult.paypal_client_id, {
        total: calculationResult.total,
        sub_total: calculationResult.sub_total,
        shipping_total: calculationResult.shipping_total,
        discount_total: calculationResult.discount_total,
        tip_total: calculationResult.tip_total,
        line_items: validationResult.line_items,
        invoice_id: validationResult.invoice_id,
        funding_source: validationResult.funding_source,
        currency: validationResult.currency,
        paypal_client_id: validationResult.paypal_client_id
      });
    } catch (paypalError) {
      console.timeEnd(`[${validationResult.domain}] createDraftOrderForPayPal - Create PayPal Order`);
      logger.error(`[${validationResult.domain}] Error in PayPal order creation step:`, paypalError);
      throw paypalError;
    }
    console.timeEnd(`[${validationResult.domain}] createDraftOrderForPayPal - Create PayPal Order`);

    // Update Draft Order section
    console.time(`[${validationResult.domain}] createDraftOrderForPayPal - Update Draft Order - PayPal ID: ${paypalOrder.id}`);
    try {
      await draftOrder.update({
        paypal_order_id: paypalOrder.id,
      });
    } catch (updateError) {
      console.timeEnd(`[${validationResult.domain}] createDraftOrderForPayPal - Update Draft Order - PayPal ID: ${paypalOrder.id}`);
      logger.error(`[${validationResult.domain}] Error in draft order update step - PayPal ID: ${paypalOrder.id}:`, updateError);
      throw updateError;
    }
    console.timeEnd(`[${validationResult.domain}] createDraftOrderForPayPal - Update Draft Order - PayPal ID: ${paypalOrder.id}`);

    logger.info(`[${validationResult.domain}] Draft order created successfully - PayPal ID: ${paypalOrder.id}`, {
      orderId: draftOrder.id,
      orderKey: calculationResult.order_key,
      paypalOrderId: paypalOrder.id,
      currency: validationResult.currency,
      domain: validationResult.domain
    });

    console.timeEnd("createDraftOrderForPayPal - Total");
    res.status(201).json({
      id: paypalOrder.id,
      currency: validationResult.currency
    });
  } catch (error) {
    console.timeEnd("createDraftOrderForPayPal - Total");
    const domain = error.validationResult?.domain || 'unknown';
    logger.error(`[${domain}] Error in createDraftOrderForPayPal:`, error);
    res.status(500).json({ error: error.message || "Internal Server Error" });
  }
};

const handleCapturePayPal = async (req, res) => {
  console.time("handleCapturePayPal - Total");
  let order;
  try {
    const { orderId } = req.params;
    console.time(`[PayPal ID: ${orderId}] handleCapturePayPal - Initial Validation & Order Lookup`);
    if (!orderId) {
      console.timeEnd(`[PayPal ID: ${orderId}] handleCapturePayPal - Initial Validation & Order Lookup`);
      console.timeEnd("handleCapturePayPal - Total");
      return res.status(400).json({ error: "PayPal Order ID is required" });
    }
    order = await Order.findOne({ where: { paypal_order_id: orderId } });
    if (!order) {
      logger.error(`[PayPal ID: ${orderId}] Order not found for PayPal order ID:`, orderId);
      console.timeEnd(`[PayPal ID: ${orderId}] handleCapturePayPal - Initial Validation & Order Lookup`);
      console.timeEnd("handleCapturePayPal - Total");
      return res.status(404).json({ error: "Order not found" });
    }
    console.timeEnd(`[PayPal ID: ${orderId}] handleCapturePayPal - Initial Validation & Order Lookup`);

    console.time(`[${order.domain}] [PayPal ID: ${orderId}] handleCapturePayPal - Check PayPal Order Status`);
    let paypalOrderStatus;
    try {
      paypalOrderStatus = await checkPaymentStatus(orderId, order.paypal_client_id);
    } catch (statusError) {
      console.timeEnd(`[${order.domain}] [PayPal ID: ${orderId}] handleCapturePayPal - Check PayPal Order Status`);
      logger.error(`[${order.domain}] [PayPal ID: ${orderId}] Error checking PayPal order status before capture`, { orderId, error: statusError.message });
      await TransactionLogger.logFinalOutcome({
        orderId: order.id,
        eventType: "CAPTURE_ERROR",
        paypalOrderId: orderId,
        notes: `Error checking PayPal order status: ${statusError.message}`,
        paypalApiResponse: statusError,
        order: order
      });
      console.timeEnd("handleCapturePayPal - Total");
      return res.status(500).json({
        error: "Failed to check PayPal order status",
        details: statusError.message
      });
    }
    console.timeEnd(`[${order.domain}] [PayPal ID: ${orderId}] handleCapturePayPal - Check PayPal Order Status`);

    if (paypalOrderStatus !== "APPROVED") {
      logger.error(`[${order.domain}] [PayPal ID: ${orderId}] PayPal order is not in APPROVED state, cannot capture`, { orderId, paypalOrderStatus });
      await TransactionLogger.logFinalOutcome({
        orderId: order.id,
        eventType: "CAPTURE_FAILED",
        paypalOrderId: orderId,
        notes: `Order not approved: ${paypalOrderStatus}`,
        paypalApiResponse: { orderStatus: paypalOrderStatus },
        order: order
      });
      console.timeEnd("handleCapturePayPal - Total");
      return res.status(400).json({
        error: "Order is not approved for capture",
        details: `Current PayPal order status: ${paypalOrderStatus}`,
        code: "ORDER_NOT_APPROVED"
      });
    }

    console.time(`[${order.domain}] [PayPal ID: ${orderId}] handleCapturePayPal - PayPal Capture API Call`);
    try {
      const captureData = await capturePayPalOrder(
        order.paypal_client_id,
        orderId,
        order.domain,
        order.orderData?.meta_data || []
      );
      console.timeEnd(`[${order.domain}] [PayPal ID: ${orderId}] handleCapturePayPal - PayPal Capture API Call`);

      console.time(`[${order.domain}] [PayPal ID: ${orderId}] handleCapturePayPal - Process Capture Response`);
      const captureStatus = captureData.status;
      const captureDetails = captureData.purchase_units[0]?.payments?.captures?.[0];
      const captureAmount = parseFloat(captureDetails?.amount?.value || 0);
      const expectedAmount = parseFloat(order.orderData.total || 0);
      console.timeEnd(`[${order.domain}] [PayPal ID: ${orderId}] handleCapturePayPal - Process Capture Response`);

      if (captureStatus === "COMPLETED") {
        const transactionId = captureDetails?.id;
        console.time(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPal - Amount Validation`);
        // Validate amount
        if (Math.abs(captureAmount - expectedAmount) > 0.01) {
          console.timeEnd(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPal - Amount Validation`);
          await TransactionLogger.logFinalOutcome({
            orderId: order.id,
            eventType: "CAPTURE_ERROR",
            paypalOrderId: orderId,
            transactionId: captureDetails?.id,
            oldStatus: order.status,
            newStatus: order.status,
            paypalApiResponse: captureData,
            notes: `Amount mismatch: captured ${captureAmount}, expected ${expectedAmount}`,
            order: order
          });
          console.timeEnd("handleCapturePayPal - Total");
          return res.status(400).json({
            error: "Amount mismatch",
            details: `Captured ${captureAmount}, expected ${expectedAmount}`,
            code: "AMOUNT_MISMATCH"
          });
        }
        console.timeEnd(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPal - Amount Validation`);

        console.time(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPal - Process Shipping & Billing Data`);
        // Update order
        const shipping = captureData.purchase_units[0]?.shipping;
        const { payer } = captureData;
        const shippingData = shipping ? {
          city: shipping.address.admin_area_2,
          state: shipping.address.admin_area_1,
          country: shipping.address.country_code,
          postcode: shipping.address.postal_code,
          address_1: shipping.address.address_line_1,
          address_2: shipping.address.address_line_2 || "",
          last_name: shipping.name.full_name.split(" ").pop(),
          first_name: shipping.name.full_name.split(" ").shift(),
        } : {};
        const updatedOrderData = {
          ...order.orderData,
          billing: {
            ...shippingData,
            phone: payer.phone?.phone_number?.national_number || "",
            email: payer.email_address,
          },
          set_paid: true,
          shipping: shippingData,
        };
        console.timeEnd(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPal - Process Shipping & Billing Data`);

        console.time(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPal - Update Order in DB`);
        const { updatedOrder, stats } = await updateOrderAfterPayPalCapture(
          order,
          captureData,
          updatedOrderData
        );
        const formattedStats = formatStats(stats);
        console.timeEnd(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPal - Update Order in DB`);

        console.time(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] [Order ID: ${updatedOrder.id}] handleCapturePayPal - Log Transaction Success`);
        await TransactionLogger.logFinalOutcome({
          orderId: updatedOrder.id,
          eventType: "CAPTURE_SUCCESS",
          paypalOrderId: orderId,
          transactionId: captureDetails?.id,
          oldStatus: order.status,
          newStatus: "processing",
          paypalApiResponse: captureData,
          notes: `Capture completed successfully. Amount: ${captureDetails?.amount?.value}`,
          order: order
        });

        // Log successful capture with all important IDs
        logger.info(`[${order.domain}] PayPal capture completed successfully - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${updatedOrder.id}`, {
          domain: order.domain,
          paypalOrderId: orderId,
          transactionId: transactionId,
          orderId: updatedOrder.id,
          orderKey: updatedOrder.order_key,
          amount: captureDetails?.amount?.value,
          currency: captureDetails?.amount?.currency_code
        });

        console.timeEnd(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] [Order ID: ${updatedOrder.id}] handleCapturePayPal - Log Transaction Success`);

        console.time(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] [Order ID: ${updatedOrder.id}] handleCapturePayPal - Build Response`);
        // Build response
        const responseOrder = {
          id: updatedOrder.id,
          order_key: updatedOrder.order_key,
          domain: updatedOrder.domain,
          transaction_id: updatedOrder.transaction_id,
          orderData: {
            total: updatedOrder.orderData.total,
            sub_total: updatedOrder.orderData.sub_total,
            shipping_total: updatedOrder.orderData.shipping_total,
            billing: updatedOrder.orderData.billing,
            shipping: updatedOrder.orderData.shipping,
            line_items: updatedOrder.orderData.line_items,
            payment_method: updatedOrder.orderData.payment_method,
            payment_method_title: updatedOrder.orderData.payment_method_title,
            set_paid: updatedOrder.orderData.set_paid,
            invoice_id: updatedOrder.orderData.invoice_id,
            funding_source: updatedOrder.orderData.funding_source,
          },
          createdAt: updatedOrder.createdAt,
          updatedAt: updatedOrder.updatedAt,
        };
        console.timeEnd(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] [Order ID: ${updatedOrder.id}] handleCapturePayPal - Build Response`);

        console.timeEnd("handleCapturePayPal - Total");
        // Return response immediately
        res.status(200).json(responseOrder);

        // Non-blocking async operations - execute after response is sent
        setImmediate(async () => {
          console.time(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] [Order ID: ${updatedOrder.id}] handleCapturePayPal - Background - Email Queue`);
          // Queue order confirmation email
          if (!config.disableEmailSending) {
            try {
              await sendToQueue("EMAIL", "order_confirmation", {
                id: updatedOrder.id,
                order_key: updatedOrder.order_key,
                domain: updatedOrder.domain,
                transaction_id: updatedOrder.transaction_id,
                orderData: updatedOrder.orderData,
                status: updatedOrder.status,
                createdAt: updatedOrder.createdAt,
                updatedAt: updatedOrder.updatedAt,
              });
              logger.info(`[${order.domain}] Order confirmation email queued - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${updatedOrder.id}`, {
                orderId: updatedOrder.id,
                paypalOrderId: orderId,
                transactionId: transactionId,
                domain: order.domain
              });
            } catch (emailError) {
              logger.error(`[${order.domain}] Failed to queue order confirmation email - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${updatedOrder.id}`, {
                error: emailError.message,
                stack: emailError.stack,
                orderId: updatedOrder.id,
                paypalOrderId: orderId,
                transactionId: transactionId,
                domain: order.domain
              });
            }
          } else {
            logger.info(
              `[${order.domain}] Email sending is disabled. Skipping order confirmation email - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${updatedOrder.id}`,
              {
                orderId: updatedOrder.id,
                paypalOrderId: orderId,
                transactionId: transactionId,
                domain: order.domain
              }
            );
          }
          console.timeEnd(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] [Order ID: ${updatedOrder.id}] handleCapturePayPal - Background - Email Queue`);

          console.time(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] [Order ID: ${updatedOrder.id}] handleCapturePayPal - Background - TrueStore Queue`);
          // Send to TrueStore
          if (!config.disableTrueStoreSync) {
            try {
              await sendToQueue("TRUESTORE", "new_order", {
                order: updatedOrder,
                stats: formattedStats
              });
              logger.info(`[${order.domain}] Order queued for TrueStore - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${updatedOrder.id}`, {
                orderId: updatedOrder.id,
                orderKey: updatedOrder.order_key,
                domain: updatedOrder.domain,
                paypalOrderId: orderId,
                transactionId: transactionId
              });
            } catch (trueStoreError) {
              logger.error(`[${order.domain}] Error queueing order for TrueStore - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${updatedOrder.id}`, {
                orderId: updatedOrder.id,
                orderKey: updatedOrder.order_key,
                domain: updatedOrder.domain,
                paypalOrderId: orderId,
                transactionId: transactionId,
                error: trueStoreError.message,
                stack: trueStoreError.stack,
              });
            }
          } else {
            logger.info(`[${order.domain}] TrueStore sync is disabled. Skipping order sync - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${updatedOrder.id}`, {
              orderId: updatedOrder.id,
              paypalOrderId: orderId,
              transactionId: transactionId,
              domain: order.domain
            });
          }
          console.timeEnd(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] [Order ID: ${updatedOrder.id}] handleCapturePayPal - Background - TrueStore Queue`);
        });
        
        return;

      } else if (captureStatus === "PENDING") {
        const transactionId = captureDetails?.id;
        console.time(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPal - Handle PENDING Status`);
        await order.update({
          status: "pending",
          transaction_id: captureDetails?.id || null,
          orderData: {
            ...order.orderData,
            set_paid: false,
            payment_status: "pending",
            capture_status: "PENDING",
            capture_reason: captureDetails?.status_details?.reason || "Under review"
          }
        });
        await TransactionLogger.logFinalOutcome({
          orderId: order.id,
          eventType: "CAPTURE_PENDING",
          paypalOrderId: orderId,
          transactionId: captureDetails?.id,
          oldStatus: order.status,
          newStatus: "pending",
          paypalApiResponse: captureData,
          notes: `Capture pending: ${captureDetails?.status_details?.reason || 'Payment under review'}`,
          order: order
        });

        logger.info(`[${order.domain}] PayPal capture pending - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${order.id}`, {
          domain: order.domain,
          paypalOrderId: orderId,
          transactionId: transactionId,
          orderId: order.id,
          reason: captureDetails?.status_details?.reason || 'Payment under review'
        });

        console.timeEnd(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPal - Handle PENDING Status`);
        console.timeEnd("handleCapturePayPal - Total");
        return res.status(202).json({
          id: order.id,
          status: "pending",
          message: "Payment is pending review",
          details: "You will be notified when payment is completed",
          capture_status: "PENDING",
          transaction_id: captureDetails?.id
        });
      } else if (captureStatus === "DECLINED") {
        const transactionId = captureDetails?.id;
        console.time(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPal - Handle DECLINED Status`);
        await order.update({
          status: "failed",
          orderData: {
            ...order.orderData,
            set_paid: false,
            payment_status: "failed",
            capture_status: "DECLINED"
          }
        });
        await TransactionLogger.logFinalOutcome({
          orderId: order.id,
          eventType: "CAPTURE_FAILED",
          paypalOrderId: orderId,
          transactionId: captureDetails?.id,
          oldStatus: order.status,
          newStatus: "failed",
          paypalApiResponse: captureData,
          notes: `Capture declined`,
          order: order
        });

        logger.error(`[${order.domain}] PayPal capture declined - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${order.id}`, {
          domain: order.domain,
          paypalOrderId: orderId,
          transactionId: transactionId,
          orderId: order.id
        });

        console.timeEnd(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPal - Handle DECLINED Status`);
        console.timeEnd("handleCapturePayPal - Total");
        return res.status(400).json({
          error: "Payment declined",
          details: "Payment was declined. Try a different payment method.",
          code: "CAPTURE_DECLINED"
        });
      } else {
        const transactionId = captureDetails?.id;
        console.time(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPal - Handle Unknown Status`);
        await TransactionLogger.logFinalOutcome({
          orderId: order.id,
          eventType: "CAPTURE_ERROR",
          paypalOrderId: orderId,
          transactionId: captureDetails?.id,
          oldStatus: order.status,
          newStatus: order.status,
          paypalApiResponse: captureData,
          notes: `Unknown capture status: ${captureStatus}`,
          order: order
        });

        logger.error(`[${order.domain}] Unknown PayPal capture status - PayPal ID: ${orderId}, Transaction ID: ${transactionId}, Order ID: ${order.id}`, {
          domain: order.domain,
          paypalOrderId: orderId,
          transactionId: transactionId,
          orderId: order.id,
          captureStatus: captureStatus
        });

        console.timeEnd(`[${order.domain}] [PayPal ID: ${orderId}] [Transaction ID: ${transactionId}] handleCapturePayPal - Handle Unknown Status`);
        console.timeEnd("handleCapturePayPal - Total");
        return res.status(500).json({
          error: "Unknown capture status",
          details: `PayPal returned unexpected status: ${captureStatus}`,
          code: "UNKNOWN_STATUS"
        });
      }
    } catch (error) {
      console.timeEnd(`[${order.domain}] [PayPal ID: ${orderId}] handleCapturePayPal - PayPal Capture API Call`);
      console.time(`[${order.domain}] [PayPal ID: ${orderId}] handleCapturePayPal - Handle Capture Error`);
      const paypalError = error.paypalError;
      const orderStatus = paypalError?.canRetry ? "pending" : "failed";
      await order.update({
        status: orderStatus,
        orderData: {
          ...order.orderData,
          payment_status: orderStatus,
          payment_error: paypalError
        }
      });
      await TransactionLogger.logFinalOutcome({
        orderId: order.id,
        eventType: "CAPTURE_ERROR",
        paypalOrderId: req.params.orderId,
        notes: `Capture error: ${error.message}`,
        paypalApiResponse: paypalError || null,
        order: order
      });

      logger.error(`[${order.domain}] PayPal capture error - PayPal ID: ${orderId}, Order ID: ${order.id}`, {
        domain: order.domain,
        paypalOrderId: orderId,
        orderId: order.id,
        error: error.message,
        paypalError: paypalError
      });

      console.timeEnd(`[${order.domain}] [PayPal ID: ${orderId}] handleCapturePayPal - Handle Capture Error`);
      console.timeEnd("handleCapturePayPal - Total");
      return res.status(paypalError?.status || 500).json({
        error: paypalError?.error || "Internal server error",
        details: paypalError?.details || "An unexpected error occurred",
        code: paypalError?.code
      });
    }
  } catch (error) {
    console.timeEnd("handleCapturePayPal - Total");
    const domain = order?.domain || 'unknown';
    const paypalOrderId = req.params?.orderId || 'unknown';
    const dbOrderId = order?.id || 'unknown';
    logger.error(`[${domain}] Error processing PayPal approval - PayPal ID: ${paypalOrderId}, Order ID: ${dbOrderId}:`, error);
    res.status(500).json({
      error: "Internal server error",
      details: "An unexpected error occurred while processing the payment"
    });
  }
};

export {
  updateOrderTracking,
  updateOrderTrackingWithoutEmail,
  createOrder,
  getOrderSum,
  analyzeOrders,
  getOrders,
  getImportedOrders,
  getOrderById,
  addNoteToOrder,
  getNotesForOrder,
  updateOrder,
  createDraftOrderForPayPal,
  handleCapturePayPal,
};
