import Order from '../models/order.js';
import logger from '../utils/logger.js';

/**
 * Public API controller to get order information by order key
 * This endpoint is used by external systems to check order status
 * Domain validation is handled by middleware before reaching this controller
 */
export const getOrderByKey = async (req, res) => {
  const startTime = Date.now();
  let orderKey;
  
  try {
    orderKey = req.params.orderKey;
    const requestDomain = req.get('x-domain');
    
    // The order validation is already done in middleware, so we can trust req.validatedOrder
    const validatedOrderInfo = req.validatedOrder;
    
    if (!validatedOrderInfo) {
      logger.error('Missing validated order info in public controller', {
        orderKey,
        requestDomain,
        ip: req.ip
      });
      
      return res.status(500).json({
        error: 'Internal server error',
        details: 'Order validation information is missing'
      });
    }

    // Fetch the complete order information
    const order = await Order.findOne({
      where: { 
        id: validatedOrderInfo.id,
        order_key: orderKey 
      }
    });

    if (!order) {
      logger.warn('Order not found in public controller despite validation', {
        orderKey,
        requestDomain,
        validatedOrderId: validatedOrderInfo.id,
        ip: req.ip
      });
      
      return res.status(404).json({
        error: 'Order not found',
        details: 'Order information is not available'
      });
    }

    // Format response with only necessary public information
    const publicOrderInfo = {
      order_key: order.order_key,
      domain: order.domain,
      status: order.status,
      tracking_id: order.tracking_id,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      // Include basic order data but filter sensitive information
      orderData: {
        billing: order.orderData?.billing ? {
          first_name: order.orderData.billing.first_name,
          last_name: order.orderData.billing.last_name,
          email: order.orderData.billing.email,
          phone: order.orderData.billing.phone,
          // Exclude sensitive billing details like address, payment info
        } : null,
        shipping: order.orderData?.shipping ? {
          first_name: order.orderData.shipping.first_name,
          last_name: order.orderData.shipping.last_name,
          // Include shipping address as it's needed for tracking
          address_1: order.orderData.shipping.address_1,
          address_2: order.orderData.shipping.address_2,
          city: order.orderData.shipping.city,
          state: order.orderData.shipping.state,
          postcode: order.orderData.shipping.postcode,
          country: order.orderData.shipping.country
        } : null,
        line_items: order.orderData?.line_items?.map(item => ({
          name: item.name,
          quantity: item.quantity,
          total: item.total,
          sku: item.sku
        })) || [],
        total: order.orderData?.total,
        currency: order.orderData?.currency || 'USD'
      }
    };

    const duration = Date.now() - startTime;
    
    logger.info('Public order retrieved successfully', {
      orderKey,
      domain: requestDomain,
      orderId: order.id,
      duration: `${duration}ms`,
      ip: req.ip
    });

    res.json({
      success: true,
      data: publicOrderInfo
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    
    logger.error('Error in public order controller', {
      message: error.message,
      stack: error.stack,
      orderKey,
      requestDomain: req.get('x-domain'),
      duration: `${duration}ms`,
      ip: req.ip
    });
    
    res.status(500).json({
      error: 'Internal server error',
      details: 'An error occurred while retrieving order information'
    });
  }
};
