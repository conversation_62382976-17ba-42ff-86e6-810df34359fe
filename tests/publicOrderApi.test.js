import request from 'supertest';
import express from 'express';
import { getOrderByKey } from '../src/controllers/publicOrderController.js';
import { validateDomainForOrder } from '../src/middleware/domainValidationMiddleware.js';
import { orderCheckRateLimit } from '../src/middleware/rateLimitMiddleware.js';
import Order from '../src/models/order.js';

// Mock the Order model
jest.mock('../src/models/order.js');
jest.mock('../src/utils/logger.js');

// Create test app
const app = express();
app.use(express.json());

// Test route with middleware
app.get('/public/v3/orders/check/:orderKey', 
  orderCheckRateLimit,
  validateDomainForOrder,
  getOrderByKey
);

describe('Public Order API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /public/v3/orders/check/:orderKey', () => {
    const mockOrder = {
      id: 1,
      order_key: 'test123456789',
      domain: 'example.com',
      status: 'processing',
      tracking_id: 'TRACK123',
      createdAt: '2024-01-01T00:00:00.000Z',
      updatedAt: '2024-01-01T00:00:00.000Z',
      orderData: {
        billing: {
          first_name: 'John',
          last_name: 'Doe',
          email: '<EMAIL>',
          phone: '+1234567890'
        },
        shipping: {
          first_name: 'John',
          last_name: 'Doe',
          address_1: '123 Main St',
          city: 'New York',
          state: 'NY',
          postcode: '10001',
          country: 'US'
        },
        line_items: [
          {
            name: 'Test Product',
            quantity: 1,
            total: '29.99',
            sku: 'TEST-001'
          }
        ],
        total: '29.99',
        currency: 'USD'
      }
    };

    test('should return 400 when x-domain header is missing', async () => {
      const response = await request(app)
        .get('/public/v3/orders/check/test123456789')
        .expect(400);

      expect(response.body).toEqual({
        error: 'Missing required header',
        details: 'x-domain header is required to access order information'
      });
    });

    test('should return 404 when order is not found', async () => {
      Order.findOne.mockResolvedValue(null);

      const response = await request(app)
        .get('/public/v3/orders/check/nonexistent')
        .set('x-domain', 'example.com')
        .expect(404);

      expect(response.body).toEqual({
        error: 'Order not found',
        details: 'No order found with the provided order key'
      });
    });

    test('should return 403 when domain does not match', async () => {
      Order.findOne.mockResolvedValue({
        id: 1,
        domain: 'different.com',
        order_key: 'test123456789'
      });

      const response = await request(app)
        .get('/public/v3/orders/check/test123456789')
        .set('x-domain', 'example.com')
        .expect(403);

      expect(response.body).toEqual({
        error: 'Domain access denied',
        details: 'The provided domain does not match the order domain'
      });
    });

    test('should return order data when domain matches', async () => {
      // Mock for domain validation
      Order.findOne.mockResolvedValueOnce({
        id: 1,
        domain: 'example.com',
        order_key: 'test123456789'
      });
      
      // Mock for full order retrieval
      Order.findOne.mockResolvedValueOnce(mockOrder);

      const response = await request(app)
        .get('/public/v3/orders/check/test123456789')
        .set('x-domain', 'example.com')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.order_key).toBe('test123456789');
      expect(response.body.data.domain).toBe('example.com');
      expect(response.body.data.status).toBe('processing');
      expect(response.body.data.orderData.billing.first_name).toBe('John');
    });
  });
});
