import request from "supertest";
import express from "express";
import {
  createOrder,
  updateSaleReport,
  updateDomainReport,
} from "../controllers/order.js";
import Order from "../models/order.js";
import logger from "../utils/logger.js";

// Tạo ứng dụng Express và đăng ký route
const app = express();
app.use(express.json());
app.post("/createOrder", createOrder);

// <PERSON><PERSON> c<PERSON> hàm cần thiết
jest.mock("../models/order.js");
jest.mock("../utils/logger.js");
jest.mock("../controllers/order.js", () => ({
  ...jest.requireActual("../controllers/order.js"),
  updateSaleReport: jest.fn(),
  updateDomainReport: jest.fn(),
}));

describe("POST /createOrder", () => {
  it("should return 400 if domain, transaction_id, or orderData is missing", async () => {
    const response = await request(app).post("/createOrder").send({
      domain: "example.com",
      transaction_id: "12345",
      // Missing orderData
    });

    expect(response.status).toBe(400);
    expect(response.text).toBe(
      "domain, transaction_id, and orderData are required"
    );
  });

  it("should create a new order and return 201", async () => {
    const orderData = {
      total: "44.98",
      billing: {
        city: "San Jose",
        email: "<EMAIL>",
        phone: "",
        state: "CA",
        country: "US",
        postcode: "95131",
        address_1: "1 Main St",
        address_2: "",
        last_name: "Doe",
        first_name: "John",
      },
      set_paid: true,
      shipping: {
        city: "San Jose",
        state: "CA",
        country: "US",
        postcode: "95131",
        address_1: "1 Main St",
        address_2: "",
        last_name: "Doe",
        first_name: "John",
      },
      meta_data: [
        { key: "ip", value: "*************" },
        {
          key: "UA",
          value:
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        },
        { key: "QUERY", value: "shop1.truestore.com/product/aipsneaker37" },
        { key: "invoice_id", value: "shop1truestore-7vcky-60767" },
        { key: "_ppcp_paypal_order_id", value: "5TX0974764421153W" },
        { key: "_ppcp_paypal_intent", value: "CAPTURE" },
        { key: "_ppcp_paypal_payment_mode", value: "live" },
      ],
      line_items: [
        {
          name: "⭐Winter Sale 50% OFF ⭐ Men's AIP Sneaker 2024",
          image:
            "https://admin.1siteclone.com/wp-content/uploads/2024/03/dd26ed320dee93ea3d63119dfe7595fa.jpg",
          price: 39.99,
          total: "39.99",
          quantity: 1,
          meta_data: [
            { id: 0, name: "COLOR", option: "BROWN" },
            { id: 0, name: "SIZE ( US )", option: "6" },
          ],
          product_id: 12335,
          product_link: "https://admin.1siteclone.com/product/aipsneaker37/",
          variation_id: 12360,
        },
      ],
      payment_method: "ppcp-gateway",
      shipping_lines: [{ total: "4.99", method_id: "flat_rate" }],
      shipping_total: "4.99",
      transaction_id: "5TX0974764421153W",
      payment_method_title: "Paypal",
    };
    const newOrder = {
      id: 1,
      domain: "example.com",
      transaction_id: "12345",
      orderData,
    };

    Order.create.mockResolvedValue(newOrder);
    updateSaleReport.mockResolvedValue();
    updateDomainReport.mockResolvedValue();

    const response = await request(app).post("/createOrder").send({
      domain: "example.com",
      transaction_id: "12345",
      orderData,
    });

    console.log(response.status);
    expect(response.status).toBe(201);
    expect(response.body).toEqual(newOrder);
  });

  it("should handle errors and return 500", async () => {
    const errorMessage = "Internal Server Error";
    Order.create.mockRejectedValue(new Error(errorMessage));

    const response = await request(app)
      .post("/createOrder")
      .send({
        domain: "example.com",
        transaction_id: "12345",
        orderData: { item: "item1", quantity: 2 },
      });

    expect(response.status).toBe(500);
    expect(response.text).toBe("Internal Server Error");
    expect(logger.error).toHaveBeenCalledWith(
      "Error creating order",
      expect.objectContaining({
        message: errorMessage,
      })
    );
  });
});
