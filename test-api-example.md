# Test API Example

## Request
```bash
curl -H "x-domain: oplexuk.com" \
     https://api-order-service.truestore.us/api/public/v3/orders/check/1750016047691ae1wM2jA
```

## Expected Response
```json
{
  "success": true,
  "data": {
    "order_key": "1750016047691ae1wM2jA",
    "domain": "oplexuk.com",
    "status": "shipped",
    "tracking_id": "SDH0072287226",
    "createdAt": "2025-06-15T12:34:08.000Z",
    "updatedAt": "2025-06-17T01:37:18.000Z",
    "orderData": {
      "billing": {
        "first_name": "<PERSON>",
        "last_name": "Vognet",
        "email": "<EMAIL>",
        "phone": ""
      },
      "shipping": {
        "first_name": "<PERSON>",
        "last_name": "Vognet",
        "address_1": "2219 Malibu drive ",
        "address_2": "",
        "city": "<PERSON> ",
        "state": "FL",
        "postcode": "33511",
        "country": "US"
      },
      "line_items": [
        {
          "name": "🎁 Hot Sale 50% OFF 🔥 Solar Pool Fountain with Lights Dual Arc - Above/Inground Pool Fountain Lights with Remote Control-Mode",
          "quantity": 1,
          "total": "69.99",
          "sku": "20250611-n176-13-01-TORQ6S"
        }
      ],
      "total": "76.98",
      "currency": "USD"
    }
  }
}
```

## Error Cases

### Missing x-domain header
```bash
curl https://api-order-service.truestore.us/api/public/v3/orders/check/1750016047691ae1wM2jA
```
Response: `400 Bad Request`
```json
{
  "error": "Missing required header",
  "details": "x-domain header is required to access order information"
}
```

### Wrong domain
```bash
curl -H "x-domain: wrongdomain.com" \
     https://api-order-service.truestore.us/api/public/v3/orders/check/1750016047691ae1wM2jA
```
Response: `403 Forbidden`
```json
{
  "error": "Domain access denied",
  "details": "The provided domain does not match the order domain"
}
```

### Order not found
```bash
curl -H "x-domain: oplexuk.com" \
     https://api-order-service.truestore.us/api/public/v3/orders/check/nonexistentkey
```
Response: `404 Not Found`
```json
{
  "error": "Order not found",
  "details": "No order found with the provided order key"
}
```

### Rate limit exceeded
After 20 requests in 5 minutes:
Response: `429 Too Many Requests`
```json
{
  "error": "Too many order check requests from this IP",
  "details": "Please try again later. Rate limit: 20 requests per 5 minutes.",
  "retryAfter": 300
}
```
